<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIPO上站工具箱 - 网站开发全流程工具集合</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-start: #667eea;
            --bg-end: #764ba2;
            --card-radius: 12px;
            --card-height: 112px;
            --card-shadow: 0 8px 18px rgba(0,0,0,0.12);
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --container-padding: 20px;
            --grid-gap: 16px;
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--bg-start) 0%, var(--bg-end) 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--container-padding);
            width: 100%;
        }
        
        /* 超宽屏优化 */
        @media (min-width: 1400px) {
            .container {
                max-width: 90%;
                padding: 0 40px;
            }
        }
        
        @media (min-width: 1600px) {
            .container {
                max-width: 85%;
                padding: 0 60px;
            }
        }
        
        @media (min-width: 1920px) {
            .container {
                max-width: 80%;
                padding: 0 80px;
            }
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 25px;
        }

        /* Progress Navigation */
        .progress-nav {
            background: rgba(255,255,255,0.95);
            border-radius: var(--border-radius-lg);
            padding: 12px 16px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.1);
            position: sticky;
            top: 10px;
            z-index: 50;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .progress-nav::-webkit-scrollbar {
            display: none;
        }

        .progress-steps {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 16px;
            min-width: max-content;
            padding: 0 8px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-normal);
            font-size: 0.85rem;
            white-space: nowrap;
            flex-shrink: 0;
            min-height: 44px;
            touch-action: manipulation;
        }
        
        .step-content {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .step-emoji {
            color: lawngreen;
            font-size: 16px;
            line-height: 1;
        }
        
        .step-title {
            color: #666;
            white-space: nowrap;
        }

        .progress-step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
        }

        .progress-step.active .step-title {
            color: white;
        }

        .step-number {
            color: #666;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .progress-step.active .step-number {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        /* Search and Filter */
        .search-filter {
            background: rgba(255,255,255,0.95);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.1);
        }

        .search-box {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 14px 40px 14px 16px;
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius-md);
            font-size: 0.9rem;
            transition: var(--transition-normal);
            background: #fff;
            min-height: 48px;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-input::placeholder {
            color: #9ca3af;
        }

        .search-icon {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1rem;
        }

        .clear-search {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 14px;
            padding: 4px;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .clear-search:hover {
            background: #f0f0f0;
            color: #666;
        }

        .clear-search.show {
            display: flex;
        }



        .filter-tags {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
        }
        
        .filter-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }
        
        .filter-tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }
        
        .filter-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: lowercase;
        }
        
        .filter-tag:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }
        
        .filter-tag.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        /* Density switch */
        .density-switch {
            margin-top: 12px;
            display: none !important;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        .density-label { color: #666; font-size: 0.85rem; }
        .density-group { display: flex; gap: 8px; }
        .density-btn {
            padding: 4px 10px;
            border-radius: 999px;
            border: 1px solid #e5e7eb;
            background: #fff;
            font-size: 12px;
            color: #374151;
            cursor: pointer;
            transition: all .2s ease;
        }
        .density-btn:hover { background: #f8fafc; }
        .density-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
            border-color: transparent;
            box-shadow: 0 3px 10px rgba(102,126,234,.35);
        }

        /* Density presets */
        body.density-compact { --card-height: 112px; }
        body.density-comfortable { --card-height: 150px; }
        body.density-cozy { --card-height: 180px; }

        /* Main Content */
        .main-content {
            background: rgba(255,255,255,0.95);
            border-radius: 16px;
            padding: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.1);
        }

        .phase-section {
            margin-bottom: 40px;
        }

        .phase-title {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .phase-description {
            color: #666;
            margin-bottom: 25px;
            font-size: 1rem;
        }

        .category-section {
            margin-bottom: 30px;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .category-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .category-count {
            color: #666;
            font-size: 0.8rem;
        }

        /* Tool Grid */
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--grid-gap);
            align-items: start;
        }

        @media (max-width: 640px) {
            .tool-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            }
        }

        @media (min-width: 1200px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            }
        }
        
        /* 宽屏优化 - 大幅增加列数 */
        @media (min-width: 1400px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 24px;
            }
        }
        
        @media (min-width: 1600px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
                gap: 28px;
            }
        }
        
        @media (min-width: 1920px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 32px;
            }
        }
        
        @media (min-width: 2560px) {
            .tool-grid {
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 36px;
            }
        }

        /* Enhanced Tool Card */
        .tool-card {
            background: white;
            border-radius: var(--card-radius);
            overflow: hidden;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-normal);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0,0,0,0.06);
            position: relative;
            height: var(--card-height);
            display: flex;
            flex-direction: column;
            cursor: pointer;
            touch-action: manipulation;
            will-change: transform;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 28px rgba(0,0,0,0.15);
        }

        .tool-card:active {
            transform: translateY(0);
            transition: var(--transition-fast);
        }

        @media (hover: none) {
            .tool-card:hover {
                transform: none;
                box-shadow: var(--card-shadow);
            }
        }

        .tool-card-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-size: cover;
            background-position: center;
            z-index: 1;
            transition: transform .4s ease, filter .3s ease;
            will-change: transform;
            filter: saturate(1.05) brightness(0.95);
        }

        .tool-card-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(92, 55, 177, 0.6) 0%, rgba(92, 55, 177, 0.9) 100%);
            z-index: 1;
        }

        .tool-card:hover .tool-card-bg {
            transform: scale(1.05);
            filter: saturate(1.1) brightness(0.98);
        }

        .tool-card-content {
            position: relative;
            z-index: 3;
            padding: 12px;
            padding-top: 48px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0,0,0,0.35);
        }

        .tool-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 0;
            position: absolute;
            top: 8px;
            left: 8px;
            right: 40px;
        }

        .tool-logo {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            flex-shrink: 0;
            /* object-fit: cover; */
            box-shadow: 0 3px 8px rgba(102, 126, 234, 0.35);
            background-color: rgb(255, 255, 255, 0.90);
        }

        .tool-info { flex: 1; }

        .tool-title {
            font-size: 0.95rem;
            font-weight: 700;
            margin-bottom: 2px;
            color: #fff;
        }

        .tool-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 2px 6px;
            border-radius: 999px;
            font-size: 0.65rem;
            font-weight: 600;
            background: rgba(255,255,255,0.18);
            color: #fff;
            border: 1px solid rgba(255,255,255,0.25);
            backdrop-filter: blur(4px);
        }

        .tool-description {
            color: rgba(255,255,255,0.92);
            font-size: 0.82rem;
            line-height: 1.3;
            margin-bottom: 0;
            flex: 0 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tool-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 8px;
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            text-transform: lowercase;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .tag.tag-highlighted {
            background: rgba(255, 255, 0, 0.3);
            border-color: rgba(255, 255, 0, 0.5);
            transform: scale(1.05);
            box-shadow: 0 0 8px rgba(255, 255, 0, 0.4);
        }

        .info-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 4;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.25);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #fff;
            transition: all 0.2s ease;
            backdrop-filter: blur(4px);
            text-decoration: none;
        }

        .info-btn:hover {
            background: rgba(0,0,0,0.55);
            transform: scale(1.08);
        }

        /* Modal */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0,0,0,0.45);
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            z-index: 200;
        }
        .modal-overlay.open { display: flex; }
        .modal {
            width: min(720px, 95vw);
            max-height: 90vh;
            background: #fff;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0,0,0,0.25);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        @media (max-width: 480px) {
            .modal {
                width: 95vw;
                max-height: 85vh;
                border-radius: var(--border-radius-md);
            }

            .modal-content {
                padding: 14px 16px 18px;
                overflow-y: auto;
                flex: 1;
            }

            .modal-hero {
                height: 140px;
            }
        }
        .modal-hero {
            height: 180px;
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .modal-hero::after {
            content: '';
            position: absolute; inset: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.15), rgba(0,0,0,0.55));
        }
        .modal-content { padding: 16px 18px 20px; }
        .modal-header { display: flex; align-items: center; gap: 12px; margin-top: -24px; }
        .modal-logo {
            width: 40px; height: 40px; border-radius: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff; display: flex; align-items: center; justify-content: center; font-weight: 700;
            box-shadow: 0 6px 18px rgba(102,126,234,.5);
        }
        .modal h3 { margin: 0; font-size: 1.1rem; }
        .modal-desc { color: #4b5563; margin: 10px 0 12px; line-height: 1.6; }
        .modal-tags { display: flex; flex-wrap: wrap; gap: 8px; }
        .modal-tags .chip {
            padding: 4px 8px; font-size: 12px; border-radius: 999px;
            background: #f3f4f6; color: #374151; border: 1px solid #e5e7eb;
        }
        .modal-actions { margin-top: 16px; display: flex; gap: 10px; }
        .btn-primary { padding: 8px 14px; border-radius: 10px; color: #fff; background: linear-gradient(135deg, #667eea, #764ba2); text-decoration: none; box-shadow: 0 6px 16px rgba(102,126,234,.4); }
        .btn-primary:hover { filter: brightness(1.05); }
        .modal-close {
            position: absolute; top: 8px; right: 8px; width: 32px; height: 32px;
            border-radius: 50%; border: none; background: rgba(0,0,0,0.5); color: #fff; cursor: pointer;
            display: flex; align-items: center; justify-content: center; font-size: 18px; line-height: 1;
        }
        .modal-close:hover { background: rgba(0,0,0,0.65); }

        /* Responsive Design */
        @media (max-width: 480px) {
            :root {
                --container-padding: 12px;
                --grid-gap: 12px;
            }

            .header h1 {
                font-size: 1.8rem;
                margin-bottom: 6px;
            }

            .header p {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .progress-nav {
                padding: 8px 12px;
                margin-bottom: 20px;
            }

            .progress-steps {
                gap: 12px;
                padding: 0 4px;
            }

            .progress-step {
                padding: 6px 10px;
                font-size: 0.8rem;
                min-height: 40px;
            }

            .search-filter {
                padding: 16px;
                margin-bottom: 20px;
            }

            .search-input {
                padding: 12px 36px 12px 14px;
                font-size: 16px; /* Prevents zoom on iOS */
                min-height: 44px;
            }

            .main-content {
                padding: 20px;
            }

            .phase-title {
                font-size: 1.4rem;
            }

            .category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .filter-tag-list {
                justify-content: flex-start;
            }

            .filter-tag {
                font-size: 12px;
                padding: 6px 10px;
            }
        }

        @media (min-width: 481px) and (max-width: 768px) {
            :root {
                --container-padding: 16px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .progress-steps {
                justify-content: center;
                flex-wrap: wrap;
                gap: 12px;
            }

            .search-filter {
                padding: 18px;
            }

            .main-content {
                padding: 22px;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .progress-steps {
                justify-content: center;
                gap: 20px;
            }
        }

        @media (min-width: 1025px) {
            .progress-steps {
                justify-content: center;
                gap: 24px;
            }
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .progress-step,
            .filter-tag,
            .clear-search,
            .info-btn {
                min-height: 44px;
                min-width: 44px;
            }

            .tool-card {
                transition: var(--transition-fast);
            }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .tool-logo {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }

        /* Loading Animation */
        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 错误状态样式 */
        .error-state {
            display: none;
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .error-icon {
            font-size: 48px;
            color: #ef4444;
            margin-bottom: 16px;
        }
        
        .error-message {
            font-size: 18px;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .error-description {
            font-size: 14px;
            margin-bottom: 24px;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius-md);
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition-fast);
        }
        
        .retry-btn:hover {
            filter: brightness(1.05);
            transform: translateY(-1px);
        }
        
        /* 空状态样式 */
        .empty-state {
            display: none;
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 20px;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .empty-description {
            font-size: 14px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        /* 焦点可见性增强 */
        .tool-card:focus,
        .progress-step:focus,
        .filter-tag:focus,
        .btn-primary:focus,
        .search-input:focus {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }
        
        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --bg-start: #000000;
                --bg-end: #1a1a1a;
                --text-primary: #ffffff;
                --text-secondary: #cccccc;
            }
        }
        
        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* 打印样式 */
        @media print {
            .header,
            .progress-nav,
            .search-filter,
            .modal-overlay,
            .loading {
                display: none !important;
            }
            
            .tool-card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ccc;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🚢 AIPO上站工具箱</h1>
            <p>网站开发全流程工具集合 - 从选词到运营的完整解决方案</p>
        </header>

        <!-- Progress Navigation -->
        <nav class="progress-nav">
            <div class="progress-steps" id="progressSteps">
                <!-- 动态生成 -->
            </div>
        </nav>

        <!-- Search and Filter -->
        <div class="search-filter">
            <div class="search-box">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索工具名称、描述或标签...">
                <div class="search-icon">🔍</div>
                <button class="clear-search" id="clearSearch" onclick="clearSearch()" title="清空搜索">✕</button>
            </div>
            <div class="filter-tags">
                <span class="filter-label">标签筛选：</span>
                <div class="filter-tag-list" id="filterTagList">
                    <!-- 动态生成的标签筛选按钮 -->
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <!-- 动态生成内容 -->
        </main>

        <!-- Loading State -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在加载工具信息...</p>
        </div>
        
        <!-- Error State -->
        <div id="errorState" class="error-state">
            <div class="error-icon">⚠️</div>
            <div class="error-message">加载失败</div>
            <div class="error-description">无法加载工具数据，请检查网络连接或稍后重试</div>
            <button class="retry-btn" onclick="retryLoadData()">重新加载</button>
        </div>
        
        <!-- Empty State -->
        <div id="emptyState" class="empty-state">
            <div class="empty-icon">🔍</div>
            <div class="empty-title">没有找到匹配的工具</div>
            <div class="empty-description">尝试调整搜索关键词或筛选条件，或者清空搜索重新开始</div>
        </div>
    </div>

    <!-- Tool Info Modal -->
    <div id="toolModal" class="modal-overlay" aria-hidden="true">
        <div class="modal" role="dialog" aria-modal="true" aria-labelledby="modalTitle">
            <button class="modal-close" id="modalClose" aria-label="关闭">×</button>
            <div class="modal-hero" id="modalHero"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-logo" id="modalLogo">🔧</div>
                    <h3 id="modalTitle"></h3>
                </div>
                <p class="modal-desc" id="modalDesc"></p>
                <div class="modal-tags" id="modalTags"></div>
                <div class="modal-actions">
                    <a id="modalOpenBtn" class="btn-primary" href="#" target="_blank" rel="noopener">打开</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let toolsData = null;
        let allTools = [];

        // 加载工具数据
        async function loadToolsData() {
            try {
                const loadingEl = document.getElementById('loading');
                loadingEl.style.display = 'block';
                
                const response = await fetch('./tools-data.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                toolsData = await response.json();
                
                // 扁平化所有工具数据
                allTools = [];
                toolsData.phases.forEach(phase => {
                    phase.categories.forEach(category => {
                        category.tools.forEach(tool => {
                            allTools.push({
                                ...tool,
                                phaseId: phase.id,
                                categoryId: category.id
                            });
                        });
                    });
                });
                
                renderProgressNav();
                renderMainContent();
                generateFilterTags(toolsData);
                initializeEventListeners();
                initializeModalElements();
                setupVirtualScrolling();
                
                document.getElementById('loading').style.display = 'none';
            } catch (error) {
                console.error('加载工具数据失败:', error);
                showErrorState(error.message);
            }
        }
        
        // 显示错误状态
        function showErrorState(message = '加载失败') {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('mainContent').style.display = 'none';
            
            const errorMsg = document.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.textContent = message;
            }
        }
        
        // 重试加载数据
        function retryLoadData() {
            document.getElementById('errorState').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
            loadToolsData();
        }
        
        // 显示空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
        }
        
        // 隐藏空状态
        function hideEmptyState() {
            document.getElementById('emptyState').style.display = 'none';
        }

        // 渲染进度导航
        function renderProgressNav() {
            const progressSteps = document.getElementById('progressSteps');
            progressSteps.innerHTML = toolsData.phases.map((phase, index) => {
                // 使用更通用的正则表达式匹配任何表情符号
                const emojiMatch = phase.title.match(/^([\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}])\s*(.+)$/u);
                const emoji = emojiMatch ? emojiMatch[1] : '';
                const titleText = emojiMatch ? emojiMatch[2] : phase.title;
                
                return `
                    <a href="#phase-${phase.id}" class="progress-step ${index === 0 ? 'active' : ''}">
                        <div class="step-number">${index + 1}</div>
                        <div class="step-content">
                            <span class="step-emoji">${emoji}</span>
                            <span class="step-title">${titleText}</span>
                        </div>
                    </a>
                `;
            }).join('');
        }

        // 渲染主要内容
        function renderMainContent() {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = toolsData.phases.map(phase => `
                <section id="phase-${phase.id}" class="phase-section">
                    <h2 class="phase-title">${phase.title}</h2>
                    <p class="phase-description">${phase.description}</p>
                    ${phase.categories.map(category => `
                        <div class="category-section">
                            <div class="category-header">
                                <h3 class="category-title">${category.title}</h3>
                                <span class="category-count">${category.tools.length} 个工具</span>
                            </div>
                            <div class="tool-grid">
                                ${category.tools.map(tool => renderToolCard(tool)).join('')}
                            </div>
                        </div>
                    `).join('')}
                </section>
            `).join('');
        }

        // 渲染工具卡片
        function renderToolCard(tool) {
            const tagsHtml = tool.tags ? tool.tags.map(tag => `<span class="tag">${tag}</span>`).join('') : '';
            
            return `
                <div class="tool-card" data-tags="${tool.tags ? tool.tags.join(' ') : ''}" onclick="window.open('${tool.url}', '_blank')">
                    <div class="tool-card-bg" style="background-image: url('${tool.screenshot}')" loading="lazy"></div>
                    <a class="info-btn" href="${tool.infoUrl || tool.url}" target="_blank" rel="noopener" onclick="event.stopPropagation();" title="介绍" aria-label="查看 ${tool.title} 详情">ℹ</a>
                    <div class="tool-card-content">
                         <div class="tool-header">
                             <img class="tool-logo" src="${tool.logo}" alt="${tool.title}" loading="lazy" onerror="this.style.display='none'">
                             <div class="tool-info">
                                 <h4 class="tool-title">${tool.title}</h4>
                             </div>
                         </div>
                         <p class="tool-description" title="${tool.description}">${tool.description}</p>
                         <div class="tool-tags">
                             ${tagsHtml}
                         </div>
                     </div>
                </div>
            `;
        }
        
        // 虚拟滚动优化（适用于大量工具）
        function setupVirtualScrolling() {
            const toolGrids = document.querySelectorAll('.tool-grid');
            if (!toolGrids.length) return;
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target.querySelector('img[data-src]');
                        if (img) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(entry.target);
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });
            
            // 观察所有工具卡片
            toolGrids.forEach(grid => {
                const toolCards = grid.querySelectorAll('.tool-card');
                toolCards.forEach(card => observer.observe(card));
            });
        }

        // 初始化事件监听器
        function initializeEventListeners() {
            const searchInput = document.getElementById('searchInput');
            const filterBtns = document.querySelectorAll('.filter-btn');
            const progressSteps = document.querySelectorAll('.progress-step');

            // 搜索功能 - 添加防抖
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    filterTools();
                }, 300);
                
                // 立即更新清空按钮状态
                const clearBtn = document.getElementById('clearSearch');
                if (e.target.value) {
                    clearBtn.classList.add('show');
                } else {
                    clearBtn.classList.remove('show');
                }
            });
            
            // 支持回车键搜索
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    clearTimeout(searchTimeout);
                    filterTools();
                }
            });

            // 清空搜索按钮
            const clearSearchBtn = document.getElementById('clearSearch');
            clearSearchBtn.addEventListener('click', clearSearch);

            // 进度导航
            progressSteps.forEach(step => {
                step.addEventListener('click', (e) => {
                    e.preventDefault();
                    progressSteps.forEach(s => s.classList.remove('active'));
                    step.classList.add('active');
                    
                    const targetId = step.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // 卡片密度（强制紧凑）
            applyDensity('compact');
            setActiveDensityButton('compact');

            // 键盘导航支持
            document.addEventListener('keydown', handleKeyboardNavigation);
            
            // 焦点管理
            setupFocusManagement();

            // 滚动监听
            setupScrollObserver();
        }

        function applyDensity(density) {
            document.body.classList.remove('density-compact', 'density-comfortable', 'density-cozy');
            document.body.classList.add(`density-${density}`);
        }
        function setActiveDensityButton(density) {
            document.querySelectorAll('.density-btn').forEach(b => b.classList.toggle('active', b.dataset.density === density));
        }

        // 搜索和筛选工具
        function filterTools() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const toolCards = document.querySelectorAll('.tool-card');
            const clearBtn = document.getElementById('clearSearch');
            
            // 显示/隐藏清空按钮
            clearBtn.classList.toggle('show', !!searchTerm);
            
            // 获取激活的标签筛选
            const activeTagFilters = Array.from(document.querySelectorAll('.filter-tag.active')).map(tag => tag.textContent.toLowerCase());
            
            // 使用 DocumentFragment 批量更新 DOM
            const fragment = document.createDocumentFragment();
            let visibleCount = 0;
            
            toolCards.forEach(card => {
                const title = card.querySelector('.tool-title')?.textContent.toLowerCase() || '';
                const description = card.querySelector('.tool-description')?.textContent.toLowerCase() || '';
                const tags = (card.dataset.tags || '').toLowerCase();
                
                // 搜索匹配
                const searchMatch = !searchTerm || 
                    title.includes(searchTerm) || 
                    description.includes(searchTerm) || 
                    tags.includes(searchTerm);
                
                // 标签筛选匹配
                const tagMatch = activeTagFilters.length === 0 || 
                    activeTagFilters.some(filterTag => tags.includes(filterTag));
                
                // 同时满足搜索和标签筛选条件
                const shouldShow = searchMatch && tagMatch;
                card.style.display = shouldShow ? 'block' : 'none';
                
                if (shouldShow) {
                    visibleCount++;
                }
            });
            
            // 显示或隐藏空状态
            if (visibleCount === 0 && (searchTerm || activeTagFilters.length > 0)) {
                showEmptyState();
            } else {
                hideEmptyState();
            }
            
            // 更新分类计数
            updateCategoryCounts();
            
            // 高亮匹配的标签
            highlightMatchingTags(searchTerm);
        }
        
        // 更新分类工具计数
        function updateCategoryCounts() {
            const categoryHeaders = document.querySelectorAll('.category-header');
            categoryHeaders.forEach(header => {
                const categorySection = header.parentElement;
                const visibleCards = categorySection.querySelectorAll('.tool-card[style*="block"], .tool-card:not([style*="none"])');
                const countEl = header.querySelector('.category-count');
                if (countEl) {
                    const visibleCount = Array.from(visibleCards).filter(card => 
                        !card.style.display || card.style.display !== 'none'
                    ).length;
                    countEl.textContent = `${visibleCount} 个工具`;
                }
            });
        }
        
        // 清空搜索
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            const filterTags = document.querySelectorAll('.filter-tag');
            
            searchInput.value = '';
            filterTags.forEach(tag => tag.classList.remove('active'));
            filterTools();
        }
        
        // 高亮匹配的标签
        function highlightMatchingTags(searchTerm) {
            const allTags = document.querySelectorAll('.tag');
            
            allTags.forEach(tag => {
                tag.classList.remove('tag-highlighted');
                if (searchTerm && tag.textContent.toLowerCase().includes(searchTerm)) {
                    tag.classList.add('tag-highlighted');
                }
            });
        }
        
        // 键盘导航处理
        function handleKeyboardNavigation(e) {
            // ESC 键关闭模态框
            if (e.key === 'Escape') {
                closeModal();
                return;
            }
            
            // Ctrl/Cmd + K 快速搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('searchInput');
                searchInput.focus();
                searchInput.select();
                return;
            }
            
            // Tab 键在工具卡片间导航
            if (e.key === 'Tab') {
                const visibleCards = Array.from(document.querySelectorAll('.tool-card')).filter(card => 
                    card.style.display !== 'none'
                );
                
                if (visibleCards.length > 0) {
                    const currentFocus = document.activeElement;
                    const currentIndex = visibleCards.indexOf(currentFocus);
                    
                    if (e.shiftKey) {
                        // Shift + Tab 向前导航
                        const prevIndex = currentIndex > 0 ? currentIndex - 1 : visibleCards.length - 1;
                        if (currentIndex >= 0) {
                            e.preventDefault();
                            visibleCards[prevIndex].focus();
                        }
                    } else {
                        // Tab 向后导航
                        const nextIndex = currentIndex < visibleCards.length - 1 ? currentIndex + 1 : 0;
                        if (currentIndex >= 0) {
                            e.preventDefault();
                            visibleCards[nextIndex].focus();
                        }
                    }
                }
            }
            
            // Enter 或 Space 键激活工具卡片
            if ((e.key === 'Enter' || e.key === ' ') && e.target.classList.contains('tool-card')) {
                e.preventDefault();
                e.target.click();
            }
        }
        
        // 焦点管理设置
        function setupFocusManagement() {
            // 为工具卡片添加 tabindex
            const toolCards = document.querySelectorAll('.tool-card');
            toolCards.forEach((card, index) => {
                card.setAttribute('tabindex', '0');
                card.setAttribute('role', 'button');
                card.setAttribute('aria-label', `查看 ${card.querySelector('.tool-title')?.textContent || ''} 工具详情`);
            });
            
            // 为进度步骤添加键盘支持
            const progressSteps = document.querySelectorAll('.progress-step');
            progressSteps.forEach(step => {
                step.setAttribute('tabindex', '0');
                step.setAttribute('role', 'button');
                step.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        step.click();
                    }
                });
            });
            
            // 为筛选标签添加键盘支持
            const filterTags = document.querySelectorAll('.filter-tag');
            filterTags.forEach(tag => {
                tag.setAttribute('tabindex', '0');
                tag.setAttribute('role', 'button');
                tag.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        tag.click();
                    }
                });
            });
        }



        // 设置滚动观察器
        function setupScrollObserver() {
            const sections = document.querySelectorAll('.phase-section');
            const progressSteps = document.querySelectorAll('.progress-step');
            
            const observerOptions = {
                threshold: 0.3,
                rootMargin: '-80px 0px -80px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionId = entry.target.id;
                        const correspondingStep = document.querySelector(`[href="#${sectionId}"]`);
                        
                        if (correspondingStep) {
                            progressSteps.forEach(s => s.classList.remove('active'));
                            correspondingStep.classList.add('active');
                        }
                    }
                });
            }, observerOptions);

            sections.forEach(section => {
                observer.observe(section);
            });
        }

        // Modal 初始化
        let modalEl, modalHeroEl, modalTitleEl, modalDescEl, modalTagsEl, modalOpenBtnEl, modalCloseEl;
        function initializeModalElements() {
            modalEl = document.getElementById('toolModal');
            modalHeroEl = document.getElementById('modalHero');
            modalTitleEl = document.getElementById('modalTitle');
            modalDescEl = document.getElementById('modalDesc');
            modalTagsEl = document.getElementById('modalTags');
            modalOpenBtnEl = document.getElementById('modalOpenBtn');
            modalCloseEl = document.getElementById('modalClose');

            modalCloseEl.addEventListener('click', closeModal);
            modalEl.addEventListener('click', (e) => { if (e.target === modalEl) closeModal(); });
            document.addEventListener('keydown', (e) => { if (e.key === 'Escape') closeModal(); });
        }

        // 显示工具详情（改为自定义弹窗）
        function showToolInfo(toolId) {
            const tool = allTools.find(t => t.id === toolId);
            if (!tool) return;
            modalHeroEl.style.backgroundImage = tool.screenshot ? `url('${tool.screenshot}')` : 'none';
            modalTitleEl.textContent = tool.title;
            modalDescEl.textContent = tool.description;
            modalTagsEl.innerHTML = `
                <span class="chip">${tool.price === 'free' ? '免费' : '付费'}</span>
                <span class="chip">${tool.difficulty === 'easy' ? '易用' : tool.difficulty === 'medium' ? '中等' : '高级'}</span>
                ${tool.tags.map(tag => `<span class="chip">#${tag}</span>`).join('')}
            `;
            modalOpenBtnEl.href = tool.url;
            openModal();
        }

        function openModal() { modalEl.classList.add('open'); document.body.style.overflow = 'hidden'; }
        function closeModal() { modalEl.classList.remove('open'); document.body.style.overflow = ''; }

        // 生成标签筛选按钮
        function generateFilterTags(toolsData) {
            const allTags = new Set();
            
            // 收集所有标签
            toolsData.phases.forEach(phase => {
                phase.categories.forEach(category => {
                    category.tools.forEach(tool => {
                        tool.tags.forEach(tag => allTags.add(tag));
                    });
                });
            });
            
            // 生成筛选按钮
            const filterTagList = document.getElementById('filterTagList');
            const sortedTags = Array.from(allTags).sort();
            
            filterTagList.innerHTML = sortedTags.map(tag => 
                `<span class="filter-tag" onclick="filterByTag('${tag}')">${tag}</span>`
            ).join('');
        }
        
        // 按标签筛选
        function filterByTag(tag) {
            const searchInput = document.getElementById('searchInput');
            const filterTags = document.querySelectorAll('.filter-tag');
            
            // 更新搜索框
            if (searchInput.value === tag) {
                // 如果已经是当前标签，则清空搜索
                searchInput.value = '';
                filterTags.forEach(btn => btn.classList.remove('active'));
            } else {
                // 设置新的标签搜索
                searchInput.value = tag;
                filterTags.forEach(btn => {
                    btn.classList.toggle('active', btn.textContent === tag);
                });
            }
            
            // 执行筛选
            filterTools();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', loadToolsData);
    </script>
</body>
</html>